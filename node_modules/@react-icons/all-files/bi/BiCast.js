// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiCast = function BiCast (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M11,21.001h2C13,14.935,8.065,10,2,10v2C6.962,12,11,16.038,11,21.001z"}},{"tag":"path","attr":{"d":"M7,21.001h2C9,17.141,5.86,14,2,14v2C4.757,16,7,18.243,7,21.001z"}},{"tag":"circle","attr":{"cx":"3.5","cy":"19.5","r":"1.5"}},{"tag":"path","attr":{"d":"M20,4H4C2.897,4,2,4.897,2,6v2.052c0.68,0.025,1.349,0.094,2,0.217V6h16v13h-5.269c0.123,0.651,0.191,1.32,0.217,2H20 c1.103,0,2-0.897,2-2V6C22,4.897,21.103,4,20,4z"}}]})(props);
};
