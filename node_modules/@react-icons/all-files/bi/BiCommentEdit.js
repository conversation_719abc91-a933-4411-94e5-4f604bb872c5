// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiCommentEdit = function BiCommentEdit (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M13.771 9.123L12.372 7.725 8.503 11.589 8.503 12.987 9.901 12.987z"}},{"tag":"path","attr":{"transform":"rotate(45.001 14.264 7.232)","d":"M13.275 6.478H15.253V7.987H13.275z"}},{"tag":"path","attr":{"d":"M20,2H4C2.897,2,2,2.897,2,4v18l5.333-4H20c1.103,0,2-0.897,2-2V4C22,2.897,21.103,2,20,2z M20,16H6.667L4,18V4h16V16z"}}]})(props);
};
