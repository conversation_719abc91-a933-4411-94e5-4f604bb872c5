// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiCycling (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M11,15.414V20h2v-4.586c0-0.526-0.214-1.042-0.586-1.414l-2-2L13,9.414l2,2C15.372,11.786,15.888,12,16.414,12H20v-2 h-3.586l-3.707-3.707c-0.391-0.391-1.023-0.391-1.414,0L8,9.586C7.622,9.964,7.414,10.466,7.414,11S7.622,12.036,8,12.414 L11,15.414z"}},{"tag":"circle","attr":{"cx":"16","cy":"5","r":"2"}},{"tag":"path","attr":{"d":"M18 14c-2.206 0-4 1.794-4 4s1.794 4 4 4 4-1.794 4-4S20.206 14 18 14zM18 20c-1.103 0-2-.897-2-2s.897-2 2-2 2 .897 2 2S19.103 20 18 20zM6 22c2.206 0 4-1.794 4-4s-1.794-4-4-4-4 1.794-4 4S3.794 22 6 22zM6 16c1.103 0 2 .897 2 2s-.897 2-2 2-2-.897-2-2S4.897 16 6 16z"}}]})(props);
};
