// THIS FILE IS AUTO GENERATED
var GenIcon = require('../lib').GenIcon
module.exports.BiChart = function BiChart (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M5,21h14c1.103,0,2-0.897,2-2V5c0-1.103-0.897-2-2-2H5C3.897,3,3,3.897,3,5v14C3,20.103,3.897,21,5,21z M5,5h14l0.001,14H5 V5z"}},{"tag":"path","attr":{"d":"M13.553 11.658L9.553 9.658 7.105 14.553 8.895 15.447 10.447 12.342 14.447 14.342 16.895 9.447 15.105 8.553z"}}]})(props);
};
