// THIS FILE IS AUTO GENERATED
import { GenIcon } from '../lib';
export function BiClinic (props) {
  return GenIcon({"tag":"svg","attr":{"viewBox":"0 0 24 24"},"child":[{"tag":"path","attr":{"d":"M12.707,2.293c-0.391-0.391-1.023-0.391-1.414,0l-9,9c-0.286,0.286-0.372,0.716-0.217,1.09C2.23,12.757,2.596,13,3,13h1v2 v5c0,1.103,0.897,2,2,2h12c1.103,0,2-0.897,2-2v-5v-2h1c0.404,0,0.77-0.243,0.924-0.617c0.155-0.374,0.069-0.804-0.217-1.09 L12.707,2.293z M18.001,20H6v-5v-3v-1.586l6-6l6,6V15l0,0L18.001,20z"}},{"tag":"path","attr":{"d":"M13 10L11 10 11 13 8 13 8 15 11 15 11 18 13 18 13 15 16 15 16 13 13 13z"}}]})(props);
};
