"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  DollarSign,
  TrendingUp,
  Users,
  ArrowRight,
  Sparkles
} from "lucide-react";

interface EarningsBreakdown {
  grossRevenue: number;
  minEarnings: number;
  maxEarnings: number;
  monthlyMinEarnings: number;
  monthlyMaxEarnings: number;
  annualMinEarnings: number;
  annualMaxEarnings: number;
}

interface CategoryData {
  name: string;
  avgCPA: number;
  conversionRate: number;
  color: string;
}

const categories: CategoryData[] = [
  { name: "E-commerce", avgCPA: 2.50, conversionRate: 3.5, color: "bg-blue-500" },
  { name: "SaaS", avgCPA: 8.00, conversionRate: 2.8, color: "bg-green-500" },
  { name: "Finance", avgCPA: 12.00, conversionRate: 1.8, color: "bg-purple-500" },
  { name: "Education", avgCPA: 4.50, conversionRate: 4.2, color: "bg-orange-500" },
  { name: "Travel", avgCPA: 3.20, conversionRate: 3.8, color: "bg-cyan-500" },
  { name: "Health", avgCPA: 6.80, conversionRate: 2.5, color: "bg-pink-500" },
];

// Revenue share range for AI platforms (50-70%)
const MIN_AGENT_SHARE = 50;
const MAX_AGENT_SHARE = 70;

export default function AgentEarningsCalculator() {
  const [monthlyUsers, setMonthlyUsers] = useState(10000);
  const [customCPA, setCustomCPA] = useState(5.00);
  const [customConversionRate, setCustomConversionRate] = useState(3.0);
  const [selectedCategory, setSelectedCategory] = useState<CategoryData>(categories[1]); // SaaS default
  const [useCustomValues, setUseCustomValues] = useState(false);

  const calculateEarnings = (): EarningsBreakdown => {
    const cpa = useCustomValues ? customCPA : selectedCategory.avgCPA;
    const conversionRate = useCustomValues ? customConversionRate : selectedCategory.conversionRate;

    const conversions = monthlyUsers * (conversionRate / 100);
    const grossRevenue = conversions * cpa;

    const minEarnings = grossRevenue * (MIN_AGENT_SHARE / 100);
    const maxEarnings = grossRevenue * (MAX_AGENT_SHARE / 100);

    return {
      grossRevenue,
      minEarnings,
      maxEarnings,
      monthlyMinEarnings: minEarnings,
      monthlyMaxEarnings: maxEarnings,
      annualMinEarnings: minEarnings * 12,
      annualMaxEarnings: maxEarnings * 12,
    };
  };

  const earnings = calculateEarnings();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  return (
    <div className="w-full max-w-6xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Controls */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Configure Your Platform
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Monthly Users */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Monthly Active Users/Queries</Label>
                <div className="space-y-2">
                  <Slider
                    value={[monthlyUsers]}
                    onValueChange={(value) => setMonthlyUsers(value[0])}
                    max={100000}
                    min={1000}
                    step={1000}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>1K</span>
                    <span className="font-medium text-foreground">{formatNumber(monthlyUsers)}</span>
                    <span>100K</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Category or Custom Values */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Revenue Model</Label>
                  <Button
                    variant={useCustomValues ? "default" : "outline"}
                    size="sm"
                    onClick={() => setUseCustomValues(!useCustomValues)}
                  >
                    {useCustomValues ? "Use Category" : "Custom Values"}
                  </Button>
                </div>

                {!useCustomValues ? (
                  <div className="space-y-3">
                    <Label className="text-sm">Content Category</Label>
                    <Select
                      value={selectedCategory.name}
                      onValueChange={(value) => {
                        const category = categories.find(c => c.name === value);
                        if (category) setSelectedCategory(category);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.name} value={category.name}>
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${category.color}`} />
                              <span>{category.name}</span>
                              <Badge variant="secondary" className="ml-2">
                                ${category.avgCPA} CPA
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <div className="text-xs text-muted-foreground">
                      Avg CPA: ${selectedCategory.avgCPA} • Conversion Rate: {selectedCategory.conversionRate}%
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm">CPA Rate ($)</Label>
                      <Input
                        type="number"
                        value={customCPA}
                        onChange={(e) => setCustomCPA(parseFloat(e.target.value) || 0)}
                        min="0.10"
                        max="50.00"
                        step="0.10"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm">Conversion Rate (%)</Label>
                      <Input
                        type="number"
                        value={customConversionRate}
                        onChange={(e) => setCustomConversionRate(parseFloat(e.target.value) || 0)}
                        min="0.1"
                        max="20.0"
                        step="0.1"
                      />
                    </div>
                  </div>
                )}
              </div>


            </CardContent>
          </Card>
        </motion.div>

        {/* Results */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="space-y-6"
        >
          {/* Main Earnings Display */}
          <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-primary" />
                Your Potential Earnings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center space-y-4">
                <motion.div
                  key={`${earnings.monthlyMinEarnings}-${earnings.monthlyMaxEarnings}`}
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                >
                  <div className="text-3xl md:text-4xl font-bold text-primary">
                    {formatCurrency(earnings.monthlyMinEarnings)} - {formatCurrency(earnings.monthlyMaxEarnings)}
                  </div>
                  <div className="text-sm text-muted-foreground">potential monthly earnings</div>
                </motion.div>
                <motion.div
                  className="flex items-center justify-center gap-2 text-muted-foreground"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                >
                  <span>{formatCurrency(earnings.annualMinEarnings)} - {formatCurrency(earnings.annualMaxEarnings)} annually</span>
                  <Sparkles className="w-4 h-4" />
                </motion.div>
              </div>
            </CardContent>
          </Card>

          {/* Conversion Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="w-5 h-5" />
                Earnings from Conversions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Total Revenue Generated</span>
                  <span className="font-medium">{formatCurrency(earnings.grossRevenue)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Your Earnings Range</span>
                  <span className="font-bold text-primary text-lg">
                    {formatCurrency(earnings.minEarnings)} - {formatCurrency(earnings.maxEarnings)}
                  </span>
                </div>
                <div className="bg-muted rounded-lg p-4 text-center">
                  <div className="text-sm text-muted-foreground mb-1">Earnings per conversion</div>
                  <div className="text-xl font-bold text-foreground">
                    ${((useCustomValues ? customCPA : selectedCategory.avgCPA) * MIN_AGENT_SHARE / 100).toFixed(2)} - ${((useCustomValues ? customCPA : selectedCategory.avgCPA) * MAX_AGENT_SHARE / 100).toFixed(2)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-foreground">
                  {formatNumber(monthlyUsers * (useCustomValues ? customConversionRate : selectedCategory.conversionRate) / 100)}
                </div>
                <div className="text-xs text-muted-foreground">Monthly Conversions</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-foreground">
                  ${useCustomValues ? customCPA.toFixed(2) : selectedCategory.avgCPA.toFixed(2)}
                </div>
                <div className="text-xs text-muted-foreground">Avg. CPA Rate</div>
              </CardContent>
            </Card>
          </div>

          {/* CTA */}
          <Card className="bg-primary text-primary-foreground">
            <CardContent className="p-6 text-center">
              <h3 className="font-bold mb-2">Ready to Start Earning?</h3>
              <p className="text-sm opacity-90 mb-4">
                Join AdMesh and start earning competitive revenue from every conversion your AI platform generates.
              </p>
              <Button
                variant="secondary"
                className="w-full"
                onClick={() => window.location.href = "/auth/signin?role=agent"}
              >
                Get Started <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
