import {DocumentActionComponent, DocumentActionsResolver, useDocumentOperation, useClient} from 'sanity'
import {TrashIcon} from '@sanity/icons'
import {useCallback, useMemo, useState} from 'react'

// Custom delete action with confirmation
export const deleteAction: DocumentActionComponent = (props) => {
  const {id, type, draft, published, onComplete} = props
  const {delete: deleteOp} = useDocumentOperation(id, type)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = useCallback(() => {
    const isConfirmed = window.confirm(
      `Are you sure you want to delete this ${type}? This action cannot be undone.`
    )

    if (isConfirmed) {
      setIsDeleting(true)
      deleteOp.execute()
      onComplete()
    }
  }, [deleteOp, onComplete, type])

  return useMemo(() => ({
    label: isDeleting ? 'Deleting...' : 'Delete',
    icon: TrashIcon,
    tone: 'critical' as const,
    disabled: isDeleting || Boolean(deleteOp.disabled),
    onHandle: handleDelete,
  }), [deleteOp.disabled, handleDelete, isDeleting])
}

// Enhanced delete action for posts with reference checking
export const deletePostAction: DocumentActionComponent = (props) => {
  const {id, type, onComplete} = props
  const {delete: deleteOp} = useDocumentOperation(id, type)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = useCallback(async () => {
    // Check if post has any references (future enhancement)
    const isConfirmed = window.confirm(
      `Are you sure you want to delete this blog post? This action cannot be undone and will remove the post from your website.`
    )

    if (isConfirmed) {
      try {
        setIsDeleting(true)
        deleteOp.execute()
        console.log('Blog post deleted successfully')
        onComplete()
      } catch (error) {
        console.error('Error deleting blog post:', error)
        alert('Error deleting blog post. Please try again.')
        setIsDeleting(false)
      }
    }
  }, [deleteOp, onComplete])

  return useMemo(() => ({
    label: isDeleting ? 'Deleting...' : 'Delete Post',
    icon: TrashIcon,
    tone: 'critical' as const,
    disabled: isDeleting || Boolean(deleteOp.disabled),
    onHandle: handleDelete,
  }), [deleteOp.disabled, handleDelete, isDeleting])
}

// Enhanced delete action for authors with reference checking
export const deleteAuthorAction: DocumentActionComponent = (props) => {
  const {id, type, draft, published, onComplete} = props
  const {delete: deleteOp} = useDocumentOperation(id, type)
  const client = useClient({apiVersion: '2023-01-01'})
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = useCallback(async () => {
    try {
      const authorId = published?._id || draft?._id

      if (!authorId) {
        alert('Error: Could not determine author ID')
        return
      }

      // Check if author has any posts
      const postsWithAuthor = await client.fetch(
        `*[_type == "post" && author._ref == $authorId]`,
        {authorId}
      )

      if (postsWithAuthor.length > 0) {
        const referenceDetails = await showReferenceDetails(client, authorId, 'author')
        alert(referenceDetails || `Cannot delete this author because they have ${postsWithAuthor.length} blog post(s). Please reassign or delete those posts first.`)
        return
      }

      const isConfirmed = window.confirm(
        `Are you sure you want to delete this author? This action cannot be undone.`
      )

      if (isConfirmed) {
        setIsDeleting(true)
        deleteOp.execute()
        console.log('Author deleted successfully')
        onComplete()
      }
    } catch (error) {
      console.error('Error deleting author:', error)
      alert('Error deleting author. Please try again.')
      setIsDeleting(false)
    }
  }, [client, deleteOp, draft?._id, onComplete, published?._id])

  return useMemo(() => ({
    label: isDeleting ? 'Deleting...' : 'Delete Author',
    icon: TrashIcon,
    tone: 'critical' as const,
    disabled: isDeleting || Boolean(deleteOp.disabled),
    onHandle: handleDelete,
  }), [deleteOp.disabled, handleDelete, isDeleting])
}

// Enhanced delete action for categories with reference checking
export const deleteCategoryAction: DocumentActionComponent = (props) => {
  const {id, type, draft, published, onComplete} = props
  const {delete: deleteOp} = useDocumentOperation(id, type)
  const client = useClient({apiVersion: '2023-01-01'})
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = useCallback(async () => {
    try {
      const categoryId = published?._id || draft?._id

      if (!categoryId) {
        alert('Error: Could not determine category ID')
        return
      }

      // Check if category is used in any posts
      const postsWithCategory = await client.fetch(
        `*[_type == "post" && $categoryId in categories[]._ref]`,
        {categoryId}
      )

      if (postsWithCategory.length > 0) {
        const referenceDetails = await showReferenceDetails(client, categoryId, 'category')
        alert(referenceDetails || `Cannot delete this category because it's used in ${postsWithCategory.length} blog post(s). Please remove this category from those posts first.`)
        return
      }

      const isConfirmed = window.confirm(
        `Are you sure you want to delete this category? This action cannot be undone.`
      )

      if (isConfirmed) {
        setIsDeleting(true)
        deleteOp.execute()
        console.log('Category deleted successfully')
        onComplete()
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      alert('Error deleting category. Please try again.')
      setIsDeleting(false)
    }
  }, [client, deleteOp, draft?._id, onComplete, published?._id])

  return useMemo(() => ({
    label: isDeleting ? 'Deleting...' : 'Delete Category',
    icon: TrashIcon,
    tone: 'critical' as const,
    disabled: isDeleting || Boolean(deleteOp.disabled),
    onHandle: handleDelete,
  }), [deleteOp.disabled, handleDelete, isDeleting])
}

// Utility function to show detailed reference information
const showReferenceDetails = async (client: any, documentId: string, documentType: string) => {
  try {
    let query = ''
    let params = {}

    if (documentType === 'author') {
      query = `*[_type == "post" && author._ref == $authorId]{title, slug, publishedAt}`
      params = {authorId: documentId}
    } else if (documentType === 'category') {
      query = `*[_type == "post" && $categoryId in categories[]._ref]{title, slug, publishedAt}`
      params = {categoryId: documentId}
    }

    const references = await client.fetch(query, params)

    if (references.length > 0) {
      const referenceList = references
        .map((ref: any) => `• ${ref.title}`)
        .join('\n')

      return `This ${documentType} is referenced in ${references.length} post(s):\n\n${referenceList}\n\nPlease remove these references before deleting.`
    }

    return null
  } catch (error) {
    console.error('Error checking references:', error)
    return `Error checking references. Please try again.`
  }
}

// Document actions resolver
export const resolveDocumentActions: DocumentActionsResolver = (prev, context) => {
  const {schemaType} = context

  // Add custom delete actions based on document type
  switch (schemaType) {
    case 'post':
      return [
        ...prev.filter((action) => action.action !== 'delete'),
        deletePostAction,
      ]
    case 'author':
      return [
        ...prev.filter((action) => action.action !== 'delete'),
        deleteAuthorAction,
      ]
    case 'category':
      return [
        ...prev.filter((action) => action.action !== 'delete'),
        deleteCategoryAction,
      ]
    default:
      return prev
  }
}
