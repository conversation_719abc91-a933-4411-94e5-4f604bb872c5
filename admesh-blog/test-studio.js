#!/usr/bin/env node

/**
 * Test script to verify Sanity Studio configuration
 * Run with: node test-studio.js
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 Testing AdMesh Studio Configuration...\n')

// Test 1: Check if required files exist
const requiredFiles = [
  'sanity.config.ts',
  'structure/index.ts',
  'structure/documentActions.ts',
  'schemaTypes/index.ts',
  'schemaTypes/postType.ts',
  'schemaTypes/authorType.ts',
  'schemaTypes/categoryType.ts',
]

console.log('📁 Checking required files...')
let allFilesExist = true

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file)
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} - MISSING`)
    allFilesExist = false
  }
})

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing!')
  process.exit(1)
}

// Test 2: Check if configuration files have correct imports
console.log('\n📦 Checking imports...')

try {
  const configContent = fs.readFileSync('sanity.config.ts', 'utf8')
  
  const requiredImports = [
    'structure',
    'resolveDocumentActions',
    'structureTool',
    'visionTool'
  ]
  
  requiredImports.forEach(importName => {
    if (configContent.includes(importName)) {
      console.log(`✅ ${importName} import found`)
    } else {
      console.log(`❌ ${importName} import missing`)
    }
  })
  
} catch (error) {
  console.log('❌ Error reading sanity.config.ts:', error.message)
}

// Test 3: Check if structure file exports correctly
console.log('\n🏗️  Checking structure configuration...')

try {
  const structureContent = fs.readFileSync('structure/index.ts', 'utf8')
  
  const requiredElements = [
    'export const structure',
    'Blog Posts',
    'Authors',
    'Categories',
    'DocumentTextIcon',
    'UsersIcon',
    'TagsIcon'
  ]
  
  requiredElements.forEach(element => {
    if (structureContent.includes(element)) {
      console.log(`✅ ${element} found in structure`)
    } else {
      console.log(`❌ ${element} missing from structure`)
    }
  })
  
} catch (error) {
  console.log('❌ Error reading structure/index.ts:', error.message)
}

// Test 4: Check if document actions are properly configured
console.log('\n🎬 Checking document actions...')

try {
  const actionsContent = fs.readFileSync('structure/documentActions.ts', 'utf8')
  
  const requiredActions = [
    'deletePostAction',
    'deleteAuthorAction',
    'deleteCategoryAction',
    'resolveDocumentActions',
    'showReferenceDetails'
  ]
  
  requiredActions.forEach(action => {
    if (actionsContent.includes(action)) {
      console.log(`✅ ${action} found`)
    } else {
      console.log(`❌ ${action} missing`)
    }
  })
  
} catch (error) {
  console.log('❌ Error reading structure/documentActions.ts:', error.message)
}

// Test 5: Check package.json for required dependencies
console.log('\n📦 Checking dependencies...')

try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const dependencies = {...packageJson.dependencies, ...packageJson.devDependencies}
  
  const requiredDeps = [
    'sanity',
    '@sanity/vision',
    '@sanity/icons'
  ]
  
  requiredDeps.forEach(dep => {
    if (dependencies[dep]) {
      console.log(`✅ ${dep} v${dependencies[dep]}`)
    } else {
      console.log(`❌ ${dep} missing from dependencies`)
    }
  })
  
} catch (error) {
  console.log('❌ Error reading package.json:', error.message)
}

console.log('\n🎉 Studio configuration test completed!')
console.log('\n📚 Next steps:')
console.log('1. Run "npm install" to install dependencies')
console.log('2. Run "npm run dev" to start the studio')
console.log('3. Test delete functionality in the studio interface')
console.log('4. Check DELETE_FUNCTIONALITY.md for usage instructions')
